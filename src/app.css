@import 'tailwindcss';
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Professional Light Mode Theme */
:root {
  --color-primary: #F8F9FA;
  --color-accent: #2563EB;
  --color-accent-secondary: #7C3AED;
  --color-accent-tertiary: #059669;
  --color-text: #1A1A1A;
  --color-text-muted: #6B7280;
  --color-light: #FFFFFF;
  --color-lighter: #F8F9FA;
  --color-surface: #FFFFFF;
  --color-surface-elevated: #F3F4F6;
  --color-border: #E5E7EB;
  --color-border-light: #F3F4F6;
  --font-heading: 'Space Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-body: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  --border-radius: 12px;
  --border-radius-lg: 20px;
  --shadow-subtle: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-large: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-accent: 0 0 20px rgba(37, 99, 235, 0.15);
  --shadow-accent-secondary: 0 0 20px rgba(124, 58, 237, 0.15);
  --shadow-accent-tertiary: 0 0 20px rgba(5, 150, 105, 0.15);
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-body);
  background: var(--color-primary);
  color: var(--color-text);
  line-height: 1.6;
  overflow-x: hidden;
  font-feature-settings: 'liga' 1, 'kern' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 700;
  line-height: 1.2;
  margin: 0;
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 900;
}

h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 800;
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 700;
}

p {
  margin: 0 0 1rem 0;
}

/* Custom Utility Classes */
.text-accent {
  color: var(--color-accent);
}

.bg-primary {
  background-color: var(--color-primary);
}

.bg-dark {
  background-color: var(--color-dark);
}

.bg-darker {
  background-color: var(--color-darker);
}

.border-accent {
  border-color: var(--color-accent);
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.875rem 2.5rem;
  font-family: var(--font-heading);
  font-weight: 600;
  text-decoration: none;
  border-radius: var(--border-radius);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: none;
  font-size: 1rem;
  letter-spacing: -0.02em;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.btn-primary {
  background: var(--color-accent);
  color: var(--color-light);
  box-shadow: var(--shadow-medium);
  font-weight: 700;
}

.btn-primary:hover {
  background: #1D4ED8;
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--shadow-large), var(--shadow-accent);
}

.btn-outline {
  background: var(--color-surface);
  color: var(--color-accent);
  border: 2px solid var(--color-accent);
  box-shadow: var(--shadow-subtle);
}

.btn-outline:hover {
  background: var(--color-accent);
  color: var(--color-light);
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--shadow-large), var(--shadow-accent);
}

.btn-ghost {
  background: var(--color-surface);
  color: var(--color-accent);
  border: 1px solid var(--color-border);
  box-shadow: var(--shadow-subtle);
}

.btn-ghost:hover {
  background: var(--color-surface-elevated);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* Animation Classes */
.fade-in {
  opacity: 0;
  transform: translateY(40px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.slide-up {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s ease;
}

.slide-up.visible {
  opacity: 1;
  transform: translateY(0);
}

.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 255, 255, 0.1);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--color-accent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 255, 0.8);
}

/* Selection */
::selection {
  background-color: var(--color-accent);
  color: var(--color-dark);
}

/* Enhanced Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(0, 255, 255, 0.2); }
  50% { box-shadow: 0 0 40px rgba(0, 255, 255, 0.4); }
}

@keyframes slideInFromLeft {
  0% { transform: translateX(-100px); opacity: 0; }
  100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideInFromRight {
  0% { transform: translateX(100px); opacity: 0; }
  100% { transform: translateX(0); opacity: 1; }
}

@keyframes scaleIn {
  0% { transform: scale(0.8); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

.float {
  animation: float 6s ease-in-out infinite;
}

.glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.slide-in-left {
  opacity: 0;
  transform: translateX(-100px);
  transition: all 0.8s ease;
}

.slide-in-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.slide-in-right {
  opacity: 0;
  transform: translateX(100px);
  transition: all 0.8s ease;
}

.slide-in-right.visible {
  opacity: 1;
  transform: translateX(0);
}

.scale-in {
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.6s ease;
}

.scale-in.visible {
  opacity: 1;
  transform: scale(1);
}

/* Stagger animation delays */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }

/* Enhanced hover effects */
.hover-glow:hover {
  box-shadow: 0 0 30px rgba(0, 255, 255, 0.3);
  transform: translateY(-5px);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(45deg, var(--color-accent), #ffffff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glitch effect */
.glitch {
  position: relative;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  animation: glitch-1 0.5s infinite;
  color: #ff00ff;
  z-index: -1;
}

.glitch::after {
  animation: glitch-2 0.5s infinite;
  color: #00ffff;
  z-index: -2;
}

@keyframes glitch-1 {
  0%, 14%, 15%, 49%, 50%, 99%, 100% { transform: translate(0); }
  15%, 49% { transform: translate(-2px, -1px); }
}

@keyframes glitch-2 {
  0%, 20%, 21%, 62%, 63%, 99%, 100% { transform: translate(0); }
  21%, 62% { transform: translate(2px, 1px); }
}

/* Professional Utility Classes */
.accent-text {
  color: var(--color-accent);
}

.accent-border {
  background: var(--color-surface);
  border: 2px solid var(--color-accent);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-subtle);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid var(--color-border-light);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-subtle);
}

.accent-glow {
  box-shadow: var(--shadow-accent);
}

.hover-lift {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
}

.hover-glow {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-glow:hover {
  box-shadow: var(--shadow-large), var(--shadow-accent);
  transform: translateY(-4px);
}

.text-shadow-glow {
  text-shadow: 0 0 20px rgba(0, 245, 255, 0.5);
}

.border-accent {
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-subtle);
}

.border-accent:hover {
  border-color: var(--color-accent);
}

/* Enhanced Visual Hierarchy */
.section-padding {
  padding: 5rem 1.5rem;
}

.section-padding-lg {
  padding: 8rem 1.5rem;
}

.container-narrow {
  max-width: 800px;
  margin: 0 auto;
}

.container-wide {
  max-width: 1400px;
  margin: 0 auto;
}

.grid-auto-fit {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.grid-auto-fill {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

/* Enhanced Responsive Typography */
@media (max-width: 768px) {
  .btn {
    padding: 0.75rem 2rem;
    font-size: 0.9rem;
  }

  h1 {
    font-size: clamp(2rem, 8vw, 3.5rem);
  }

  h2 {
    font-size: clamp(1.5rem, 6vw, 2.5rem);
  }

  .section-padding {
    padding: 3rem 1rem;
  }

  .section-padding-lg {
    padding: 4rem 1rem;
  }
}

/* Hide default cursor on desktop */
@media (hover: hover) {
  * {
    cursor: none !important;
  }
}
