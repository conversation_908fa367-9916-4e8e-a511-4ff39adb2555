<script lang="ts">
	import Button from '$lib/components/Button.svelte';
	import { getServices, getFeaturedProjects, getApproaches, getPageContent } from '$lib/cms';

	const services = getServices();
	const featuredWork = getFeaturedProjects();
	const approaches = getApproaches();
	const pageContent = getPageContent('home');


</script>

<svelte:head>
	<title>{pageContent.title}</title>
	<meta name="description" content="{pageContent.description}" />
</svelte:head>

<!-- Hero Section -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-primary to-surface">
	<!-- Background Elements -->
	<div class="absolute inset-0">
		<!-- Animated background elements -->
		<div class="absolute top-1/4 left-1/4 w-64 h-64 bg-accent/5 rounded-full blur-3xl animate-pulse"></div>
		<div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent-secondary/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
	</div>

	<div class="relative z-10 text-center px-6 max-w-4xl mx-auto">
		<h1 class="text-6xl md:text-8xl font-black mb-6 fade-in">
			<span class="block text-text slide-in-left">{pageContent.hero?.title.line1}</span>
			<span class="block accent-text slide-in-right">{pageContent.hero?.title.line2}</span>
		</h1>
		<p class="text-xl md:text-2xl text-text-muted mb-8 max-w-2xl mx-auto scale-in" style="animation-delay: 0.3s;">
			{pageContent.hero?.subtitle}
		</p>
		<div class="scale-in hover-lift" style="animation-delay: 0.6s;">
			<Button href={pageContent.hero?.ctaLink} variant="primary" size="lg">
				{pageContent.hero?.ctaText}
			</Button>
		</div>
	</div>

	<!-- Scroll indicator -->
	<div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
		<div class="w-6 h-10 border-2 border-accent rounded-full flex justify-center">
			<div class="w-1 h-3 bg-accent rounded-full mt-2 animate-pulse"></div>
		</div>
	</div>
</section>

<!-- Who We Are Section -->
<section class="py-20 px-6">
	<div class="container mx-auto max-w-4xl text-center">
		<div class="slide-up">
			<h2 class="text-4xl md:text-5xl font-bold mb-8">
				We're Not Just Another Agency.
			</h2>
			<p class="text-xl text-text-muted mb-8 leading-relaxed">
				We are the digital natives who grew up with technology in our DNA. While others adapt to change,
				we create it. Our Gen Z perspective brings fresh energy to every project, combined with the
				professionalism that delivers results.
			</p>
			<Button href="/about" variant="outline">
				More About Us →
			</Button>
		</div>
	</div>
</section>

<!-- Services Section -->
<section class="section-padding-lg bg-surface-elevated">
	<div class="container-wide">
		<div class="text-center mb-20 slide-up">
			<h2 class="text-4xl md:text-5xl font-bold mb-8">What We <span class="accent-text">Do</span></h2>
			<p class="text-xl text-text-muted max-w-2xl mx-auto">
				We specialize in creating experiences that blur the line between digital and physical reality.
			</p>
		</div>

		<div class="grid-auto-fit">
			{#each services as service, index}
				<div class="slide-up hover-lift hover-glow group" style="animation-delay: {index * 0.1}s;">
					<div class="glass-effect p-8 border-accent transition-all duration-300 h-full">
						<div class="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">{service.icon}</div>
						<h3 class="text-xl font-bold mb-4 accent-text transition-all duration-300">{service.title}</h3>
						<p class="text-text-muted group-hover:text-text transition-colors duration-300">{service.description}</p>
					</div>
				</div>
			{/each}
		</div>
	</div>
</section>

<!-- Featured Work Section -->
<section class="section-padding-lg">
	<div class="container-wide">
		<div class="text-center mb-20 slide-up">
			<h2 class="text-4xl md:text-5xl font-bold mb-8">Our Work in <span class="accent-text">Action</span></h2>
			<p class="text-xl text-text-muted max-w-2xl mx-auto">
				Every project is a chance to push boundaries and create something extraordinary.
			</p>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-2 gap-12">
			{#each featuredWork as work, index}
				<div class="slide-up hover-lift group hover-glow" style="animation-delay: {index * 0.1}s;">
					<div class="relative overflow-hidden glass-effect border-accent transition-all duration-300 cursor-pointer">
						<div class="aspect-video bg-gradient-to-br from-accent/20 to-accent/5 flex items-center justify-center relative overflow-hidden">
							<div class="text-6xl opacity-50 group-hover:scale-110 transition-transform duration-500">🎨</div>
							<div class="absolute inset-0 bg-accent/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
						</div>
						<div class="p-6">
							<div class="text-sm accent-text mb-2 transition-all duration-300">{work.category}</div>
							<h3 class="text-xl font-bold mb-2 group-hover:accent-text transition-all duration-300">{work.title}</h3>
							<p class="text-text-muted mb-4 group-hover:text-text transition-colors duration-300">{work.description}</p>
							<div class="flex items-center text-accent group-hover:translate-x-2 transition-transform duration-300">
								<span class="text-sm font-medium">View Case Study</span>
								<svg class="w-4 h-4 ml-2 group-hover:rotate-45 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
								</svg>
							</div>
						</div>
					</div>
				</div>
			{/each}
		</div>

		<div class="text-center mt-12 slide-up">
			<Button href="/work" variant="outline" size="lg">
				View All Projects
			</Button>
		</div>
	</div>
</section>

<!-- Our Approach Section -->
<section class="section-padding-lg bg-surface-elevated">
	<div class="container-wide">
		<div class="text-center mb-20 slide-up">
			<h2 class="text-4xl md:text-5xl font-bold mb-8">
				We Don't Follow Trends.<br />
				<span class="accent-text">We Create Them.</span>
			</h2>
			<p class="text-xl text-text-muted max-w-2xl mx-auto">
				Our approach is built on three core principles that set us apart from traditional agencies.
			</p>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-3 gap-12">
			{#each approaches as approach, index}
				<div class="slide-up text-center" style="animation-delay: {index * 0.2}s;">
					<div class="glass-effect p-8 border-accent hover-glow transition-all duration-300 hover-lift h-full group">
						<div class="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">{approach.icon}</div>
						<h3 class="text-2xl font-bold mb-4 accent-text transition-all duration-300">{approach.title}</h3>
						<p class="text-text-muted leading-relaxed group-hover:text-text transition-colors duration-300">{approach.description}</p>
					</div>
				</div>
			{/each}
		</div>
	</div>
</section>

<!-- Final CTA Section -->
<section class="section-padding-lg relative overflow-hidden bg-gradient-to-r from-surface to-primary">
	<!-- Background elements -->
	<div class="absolute inset-0">
		<div class="absolute top-0 left-1/4 w-96 h-96 bg-accent/5 rounded-full blur-3xl"></div>
		<div class="absolute bottom-0 right-1/4 w-64 h-64 bg-accent-secondary/5 rounded-full blur-2xl"></div>
	</div>

	<div class="container-narrow text-center relative z-10">
		<div class="slide-up">
			<h2 class="text-5xl md:text-6xl font-black mb-6">
				{@html pageContent.cta?.title}
			</h2>
			<p class="text-2xl text-text-muted mb-8 max-w-2xl mx-auto">
				{pageContent.cta?.subtitle}
			</p>
			<div class="space-y-4 md:space-y-0 md:space-x-4 md:flex md:justify-center">
				<Button href={pageContent.cta?.primaryCTA.link} variant="primary" size="lg">
					{pageContent.cta?.primaryCTA.text}
				</Button>
				<Button href={pageContent.cta?.secondaryCTA.link} variant="outline" size="lg">
					{pageContent.cta?.secondaryCTA.text}
				</Button>
			</div>
		</div>
	</div>
</section>
