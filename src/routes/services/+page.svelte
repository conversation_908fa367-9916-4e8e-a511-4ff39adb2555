<script lang="ts">
	import Button from '$lib/components/Button.svelte';
	import { onMount } from 'svelte';

	let expandedService = $state<string | null>(null);

	const services = [
		{
			id: 'interactive-installations',
			title: 'Interactive Installations',
			subtitle: 'Physical meets digital in perfect harmony',
			description: 'Transform any space into an immersive experience that responds to human presence and interaction.',
			icon: '🎯',
			features: [
				'Motion-sensing installations',
				'Touch-responsive surfaces',
				'Real-time data visualization',
				'Multi-user interaction systems',
				'Environmental integration',
				'Custom hardware solutions'
			],
			process: [
				'Concept Development & Space Analysis',
				'Technical Feasibility & Prototyping',
				'Hardware Selection & Custom Development',
				'Software Integration & Testing',
				'Installation & Calibration',
				'Training & Ongoing Support'
			],
			examples: [
				'Museum interactive exhibits',
				'Retail brand activations',
				'Corporate lobby installations',
				'Event centerpieces'
			]
		},
		{
			id: 'ar-vr-experiences',
			title: 'AR/VR Experiences',
			subtitle: 'Reality reimagined through cutting-edge technology',
			description: 'Create immersive worlds and augmented realities that transport users beyond the physical realm.',
			icon: '🥽',
			features: [
				'Custom VR applications',
				'Mobile AR experiences',
				'WebXR solutions',
				'Mixed reality installations',
				'360° content creation',
				'Cross-platform compatibility'
			],
			process: [
				'Experience Design & Storyboarding',
				'3D Modeling & Environment Creation',
				'Interaction Design & User Testing',
				'Platform Optimization',
				'Quality Assurance & Performance Testing',
				'Deployment & Analytics Setup'
			],
			examples: [
				'Product visualization tools',
				'Training simulations',
				'Virtual showrooms',
				'Educational experiences'
			]
		},
		{
			id: 'projection-mapping',
			title: 'Projection Mapping',
			subtitle: 'Transform any surface into a canvas of light',
			description: 'Turn buildings, objects, and spaces into dynamic visual displays that tell compelling stories.',
			icon: '📽️',
			features: [
				'Architectural projection mapping',
				'Object-based mapping',
				'Interactive projection systems',
				'Multi-projector setups',
				'Real-time content control',
				'Weather-resistant solutions'
			],
			process: [
				'Site Survey & 3D Scanning',
				'Content Creation & Animation',
				'Projection System Design',
				'Calibration & Alignment',
				'Show Programming & Testing',
				'Live Event Management'
			],
			examples: [
				'Building facades',
				'Stage designs',
				'Product launches',
				'Festival installations'
			]
		},
		{
			id: 'creative-consulting',
			title: 'Creative Technology Consulting',
			subtitle: 'Strategic guidance for your digital transformation',
			description: 'Navigate the complex world of creative technology with expert guidance tailored to your goals.',
			icon: '💡',
			features: [
				'Technology strategy development',
				'Creative concept validation',
				'Budget planning & optimization',
				'Vendor selection & management',
				'Team training & workshops',
				'Innovation roadmapping'
			],
			process: [
				'Current State Assessment',
				'Goal Definition & Strategy Development',
				'Technology Evaluation & Selection',
				'Implementation Planning',
				'Team Training & Knowledge Transfer',
				'Ongoing Support & Optimization'
			],
			examples: [
				'Digital transformation strategies',
				'Creative technology audits',
				'Innovation workshops',
				'Team capability building'
			]
		}
	];

	function toggleService(serviceId: string) {
		expandedService = expandedService === serviceId ? null : serviceId;
	}

	onMount(() => {
		// Add smooth scrolling for anchor links
		const handleAnchorClick = (e: Event) => {
			const target = e.target as HTMLAnchorElement;
			if (target.hash) {
				e.preventDefault();
				const element = document.querySelector(target.hash);
				if (element) {
					element.scrollIntoView({ behavior: 'smooth' });
				}
			}
		};

		document.addEventListener('click', handleAnchorClick);
		return () => document.removeEventListener('click', handleAnchorClick);
	});
</script>

<svelte:head>
	<title>Services - Hitez</title>
	<meta name="description" content="Discover our creative technology services: Interactive Installations, AR/VR Experiences, Projection Mapping, and Creative Technology Consulting." />
</svelte:head>

<!-- Hero Section -->
<section class="pt-32 pb-20 px-6 relative overflow-hidden">
	<div class="absolute inset-0 bg-gradient-to-br from-primary via-dark to-primary">
		<div class="absolute top-1/3 left-1/4 w-64 h-64 bg-accent/10 rounded-full blur-3xl float"></div>
		<div class="absolute bottom-1/3 right-1/4 w-96 h-96 bg-accent/5 rounded-full blur-3xl float" style="animation-delay: -2s;"></div>
	</div>
	
	<div class="container mx-auto max-w-4xl text-center relative z-10">
		<h1 class="text-5xl md:text-7xl font-black mb-8 fade-in">
			What We <span class="gradient-text neon-glow">Create</span>
		</h1>
		<p class="text-xl md:text-2xl text-text-muted mb-8 max-w-3xl mx-auto slide-up">
			From interactive installations to immersive experiences, we bring ideas to life through cutting-edge technology and creative vision.
		</p>
		<div class="slide-up" style="animation-delay: 0.3s;">
			<Button href="#services" variant="outline" size="lg">
				Explore Services
			</Button>
		</div>
	</div>
</section>

<!-- Services Overview -->
<section class="py-20 px-6 bg-dark" id="services">
	<div class="container mx-auto max-w-6xl">
		<div class="text-center mb-16 slide-up">
			<h2 class="text-4xl md:text-5xl font-bold mb-6">Our Expertise</h2>
			<p class="text-xl text-text-muted max-w-2xl mx-auto">
				Four core services that transform ideas into unforgettable experiences.
			</p>
		</div>
		
		<div class="space-y-8">
			{#each services as service, index}
				<div class="slide-up" style="animation-delay: {index * 0.1}s;">
					<div class="glass-effect border-gradient overflow-hidden hover-glow transition-all duration-300">
						<!-- Service Header -->
						<button
							class="w-full p-8 text-left hover:glass-effect transition-all duration-300 focus:outline-none hover-lift"
							onclick={() => toggleService(service.id)}
						>
							<div class="flex items-center justify-between">
								<div class="flex items-center space-x-6">
									<div class="text-4xl">{service.icon}</div>
									<div>
										<h3 class="text-2xl font-bold gradient-text mb-2">{service.title}</h3>
										<p class="text-text-muted">{service.subtitle}</p>
									</div>
								</div>
								<div class="text-accent transition-transform duration-300 {expandedService === service.id ? 'rotate-180' : ''}">
									<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
									</svg>
								</div>
							</div>
						</button>
						
						<!-- Expandable Content -->
						{#if expandedService === service.id}
							<div class="px-8 pb-8 border-t border-accent/20">
								<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
									<!-- Description and Features -->
									<div class="space-y-6">
										<div>
											<h4 class="text-xl font-bold mb-3 text-accent">What We Do</h4>
											<p class="text-text-muted leading-relaxed">{service.description}</p>
										</div>
										
										<div>
											<h4 class="text-xl font-bold mb-3 text-accent">Key Features</h4>
											<ul class="space-y-2">
												{#each service.features as feature}
													<li class="flex items-center text-text-muted">
														<svg class="w-4 h-4 text-accent mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
															<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
														</svg>
														{feature}
													</li>
												{/each}
											</ul>
										</div>
									</div>
									
									<!-- Process and Examples -->
									<div class="space-y-6">
										<div>
											<h4 class="text-xl font-bold mb-3 text-accent">Our Process</h4>
											<ol class="space-y-2">
												{#each service.process as step, stepIndex}
													<li class="flex items-start text-text-muted">
														<span class="bg-accent text-dark rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5 flex-shrink-0">
															{stepIndex + 1}
														</span>
														{step}
													</li>
												{/each}
											</ol>
										</div>
										
										<div>
											<h4 class="text-xl font-bold mb-3 text-accent">Perfect For</h4>
											<div class="flex flex-wrap gap-2">
												{#each service.examples as example}
													<span class="bg-accent/20 text-accent px-3 py-1 rounded-full text-sm">
														{example}
													</span>
												{/each}
											</div>
										</div>
									</div>
								</div>
								
								<!-- CTA -->
								<div class="mt-8 pt-6 border-t border-accent/20">
									<div class="flex flex-col sm:flex-row gap-4">
										<Button href="/contact" variant="primary">
											Get Started
										</Button>
										<Button href="/work" variant="outline">
											See Examples
										</Button>
									</div>
								</div>
							</div>
						{/if}
					</div>
				</div>
			{/each}
		</div>
	</div>
</section>

<!-- Process Overview -->
<section class="py-20 px-6">
	<div class="container mx-auto max-w-4xl text-center">
		<div class="slide-up">
			<h2 class="text-4xl md:text-5xl font-bold mb-8">
				How We Work <span class="text-accent">Together</span>
			</h2>
			<p class="text-xl text-text-muted mb-12 max-w-2xl mx-auto">
				Every project is unique, but our approach remains consistent: collaborative, transparent, and focused on your success.
			</p>

			<div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
				<div class="slide-up" style="animation-delay: 0.1s;">
					<div class="bg-dark p-6 rounded-lg border border-accent/20 hover:border-accent/40 transition-all duration-300">
						<div class="text-4xl mb-4">🎯</div>
						<h3 class="text-xl font-bold mb-3 text-accent">Discovery</h3>
						<p class="text-text-muted">We dive deep into your vision, goals, and constraints to understand what success looks like.</p>
					</div>
				</div>

				<div class="slide-up" style="animation-delay: 0.2s;">
					<div class="bg-dark p-6 rounded-lg border border-accent/20 hover:border-accent/40 transition-all duration-300">
						<div class="text-4xl mb-4">⚡</div>
						<h3 class="text-xl font-bold mb-3 text-accent">Creation</h3>
						<p class="text-text-muted">We prototype, iterate, and refine until we've crafted an experience that exceeds expectations.</p>
					</div>
				</div>

				<div class="slide-up" style="animation-delay: 0.3s;">
					<div class="bg-dark p-6 rounded-lg border border-accent/20 hover:border-accent/40 transition-all duration-300">
						<div class="text-4xl mb-4">🚀</div>
						<h3 class="text-xl font-bold mb-3 text-accent">Launch</h3>
						<p class="text-text-muted">We ensure flawless execution and provide ongoing support to maximize your investment.</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- Final CTA -->
<section class="py-20 px-6 bg-dark">
	<div class="container mx-auto max-w-4xl text-center">
		<div class="slide-up">
			<h2 class="text-4xl md:text-5xl font-bold mb-6">
				Ready to Push <span class="text-accent">Boundaries?</span>
			</h2>
			<p class="text-xl text-text-muted mb-8 max-w-2xl mx-auto">
				Whether you have a clear vision or just a spark of an idea, we're here to help you create something extraordinary.
			</p>
			<div class="space-y-4 md:space-y-0 md:space-x-4 md:flex md:justify-center">
				<Button href="/contact" variant="primary" size="lg">
					Start Your Project
				</Button>
				<Button href="/work" variant="outline" size="lg">
					See Our Work
				</Button>
			</div>
		</div>
	</div>
</section>
