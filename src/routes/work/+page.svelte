<script lang="ts">
	import Button from '$lib/components/Button.svelte';
	import { getProjects, getFeaturedProjects, getProjectCategories, getPageContent } from '$lib/cms';

	let activeFilter = $state('all');

	const categories = getProjectCategories();
	const projects = getProjects();
	const pageContent = getPageContent('work');


	const filteredProjects = $derived(activeFilter === 'all'
		? projects
		: projects.filter(project => project.category === activeFilter));

	const featuredProjects = $derived(projects.filter(project => project.featured));

	function setFilter(categoryId: string) {
		activeFilter = categoryId;
	}
</script>

<svelte:head>
	<title>Our Work - Hitez</title>
	<meta name="description" content="Explore our portfolio of interactive installations, AR/VR experiences, projection mapping projects, and creative technology solutions." />
</svelte:head>

<!-- Hero Section -->
<section class="section-padding-lg relative overflow-hidden bg-gradient-to-br from-primary to-surface">
	<div class="absolute inset-0">
		<div class="absolute top-1/4 left-1/3 w-64 h-64 bg-accent/5 rounded-full blur-3xl float"></div>
		<div class="absolute bottom-1/3 right-1/3 w-96 h-96 bg-accent-secondary/5 rounded-full blur-3xl float" style="animation-delay: -3s;"></div>
	</div>

	<div class="container-narrow text-center relative z-10">
		<h1 class="text-5xl md:text-7xl font-black mb-8 fade-in">
			Our <span class="accent-text">Work</span>
		</h1>
		<p class="text-xl md:text-2xl text-text-muted mb-8 max-w-3xl mx-auto slide-up">
			Every project is a chance to push boundaries and create something the world has never seen before.
		</p>
	</div>
</section>

<!-- Featured Projects -->
<section class="py-20 px-6 bg-surface-elevated">
	<div class="container mx-auto max-w-6xl">
		<div class="text-center mb-16 slide-up">
			<h2 class="text-4xl md:text-5xl font-bold mb-6">Featured Work</h2>
			<p class="text-xl text-text-muted max-w-2xl mx-auto">
				Our most impactful projects that showcase the power of creative technology.
			</p>
		</div>
		
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
			{#each featuredProjects as project, index}
				<a 
					href="/work/{project.id}" 
					class="slide-up hover-lift group hover-glow block" 
					style="animation-delay: {index * 0.1}s;"
				>
					<div class="glass-effect border-accent overflow-hidden transition-all duration-300">
						<div class="aspect-video bg-gradient-to-br from-accent/20 to-accent/5 flex items-center justify-center relative overflow-hidden">
							<div class="text-8xl opacity-30 group-hover:scale-110 transition-transform duration-500">🎨</div>
							<div class="absolute inset-0 bg-accent/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
							<div class="absolute top-4 left-4">
								<span class="glass-effect accent-text px-3 py-1 rounded-full text-sm font-bold border border-accent/30">Featured</span>
							</div>
						</div>
						<div class="p-6">
							<div class="flex items-center justify-between mb-3">
								<span class="text-accent text-sm font-medium">{project.year}</span>
								<span class="text-text-muted text-sm">{project.client}</span>
							</div>
							<h3 class="text-2xl font-bold mb-2 group-hover:accent-text transition-all duration-300">{project.title}</h3>
							<p class="accent-text text-sm font-medium mb-3">{project.subtitle}</p>
							<p class="text-text-muted mb-4 leading-relaxed">{project.description}</p>
							<div class="flex flex-wrap gap-2 mb-4">
								{#each project.tags as tag}
									<span class="bg-accent/20 text-accent px-2 py-1 rounded text-xs">
										{tag}
									</span>
								{/each}
							</div>
							<div class="flex items-center text-accent group-hover:translate-x-2 transition-transform duration-300">
								<span class="text-sm font-medium">View Case Study</span>
								<svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
								</svg>
							</div>
						</div>
					</div>
				</a>
			{/each}
		</div>
	</div>
</section>

<!-- All Projects -->
<section class="py-20 px-6">
	<div class="container mx-auto max-w-6xl">
		<div class="text-center mb-16 slide-up">
			<h2 class="text-4xl md:text-5xl font-bold mb-6">All Projects</h2>
			<p class="text-xl text-text-muted max-w-2xl mx-auto mb-8">
				Explore our complete portfolio across different categories and technologies.
			</p>

			<!-- Filter Buttons -->
			<div class="flex flex-wrap justify-center gap-6">
				{#each categories as category}
					<button
						class="px-6 py-3 rounded-full border transition-all duration-300 font-medium hover-lift {
							activeFilter === category.id
								? 'glass-effect accent-text border-accent accent-glow'
								: 'glass-effect text-text border-accent/40 hover:border-accent hover:accent-text'
						}"
						onclick={() => setFilter(category.id)}
					>
						{category.label}
					</button>
				{/each}
			</div>
		</div>

		<!-- Projects Grid -->
		<div class="grid-auto-fill mt-16">
			{#each filteredProjects as project, index}
				<a
					href="/work/{project.id}"
					class="slide-up hover-lift group block"
					style="animation-delay: {index * 0.05}s;"
				>
					<div class="bg-dark rounded-lg overflow-hidden border border-accent/20 hover:border-accent/40 transition-all duration-300 h-full">
						<div class="aspect-video bg-gradient-to-br from-accent/20 to-accent/5 flex items-center justify-center relative overflow-hidden">
							<div class="text-6xl opacity-30 group-hover:scale-110 transition-transform duration-500">🎨</div>
							<div class="absolute inset-0 bg-accent/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
							{#if project.featured}
								<div class="absolute top-3 left-3">
									<span class="bg-accent text-dark px-2 py-1 rounded text-xs font-bold">Featured</span>
								</div>
							{/if}
						</div>
						<div class="p-6">
							<div class="flex items-center justify-between mb-3">
								<span class="text-accent text-sm font-medium">{project.year}</span>
								<span class="text-text-muted text-sm">{project.client}</span>
							</div>
							<h3 class="text-xl font-bold mb-2 group-hover:text-accent transition-colors duration-300">{project.title}</h3>
							<p class="text-accent text-sm font-medium mb-3">{project.subtitle}</p>
							<p class="text-text-muted text-sm mb-4 leading-relaxed line-clamp-3">{project.description}</p>
							<div class="flex flex-wrap gap-1 mb-4">
								{#each project.tags.slice(0, 2) as tag}
									<span class="bg-accent/20 text-accent px-2 py-1 rounded text-xs">
										{tag}
									</span>
								{/each}
								{#if project.tags.length > 2}
									<span class="text-text-muted text-xs px-2 py-1">
										+{project.tags.length - 2} more
									</span>
								{/if}
							</div>
							<div class="flex items-center text-accent group-hover:translate-x-2 transition-transform duration-300">
								<span class="text-sm font-medium">View Details</span>
								<svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
								</svg>
							</div>
						</div>
					</div>
				</a>
			{/each}
		</div>

		{#if filteredProjects.length === 0}
			<div class="text-center py-16">
				<div class="text-6xl mb-4 opacity-50">🔍</div>
				<h3 class="text-2xl font-bold mb-4">No projects found</h3>
				<p class="text-text-muted mb-6">Try selecting a different category to see more projects.</p>
				<Button variant="outline" onclick={() => setFilter('all')}>
					Show All Projects
				</Button>
			</div>
		{/if}
	</div>
</section>

<!-- CTA Section -->
<section class="py-20 px-6 bg-dark">
	<div class="container mx-auto max-w-4xl text-center">
		<div class="slide-up">
			<h2 class="text-4xl md:text-5xl font-bold mb-6">
				{@html pageContent.cta?.title}
			</h2>
			<p class="text-xl text-text-muted mb-8 max-w-2xl mx-auto">
				{pageContent.cta?.subtitle}
			</p>
			<div class="space-y-4 md:space-y-0 md:space-x-4 md:flex md:justify-center">
				<Button href={pageContent.cta?.primaryCTA.link} variant="primary" size="lg">
					{pageContent.cta?.primaryCTA.text}
				</Button>
				<Button href={pageContent.cta?.secondaryCTA.link} variant="outline" size="lg">
					{pageContent.cta?.secondaryCTA.text}
				</Button>
			</div>
		</div>
	</div>
</section>

<style>
	.line-clamp-3 {
		display: -webkit-box;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
